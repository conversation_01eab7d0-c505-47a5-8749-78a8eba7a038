const express = require('express');
const router = express.Router();
const {
  updateBusinessLocation,
  getBusinessLocation,
  getNearbyBusinesses,
  getAllBusinessLocations,
  deleteBusinessLocation
} = require('../controllers/locationController');
const auth = require('../middlewares/auth');

// Business location management routes (require authentication)
router.put('/business/update', auth, updateBusinessLocation);
router.get('/business/get', auth, getBusinessLocation);
router.delete('/business/delete', auth, deleteBusinessLocation);

// Public routes for finding businesses
router.get('/nearby', getNearbyBusinesses);
router.get('/all-businesses', getAllBusinessLocations);

module.exports = router;
