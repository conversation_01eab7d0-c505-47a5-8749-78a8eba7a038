import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:petcare/utlis/constants/colors.dart';
import 'package:petcare/provider/category_provider.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  @override
  void initState() {
    super.initState();
    // Fetch categories when the screen loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<CategoryProvider>(context, listen: false).fetchCategories();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 20),

              // Header with cute pets
              Container(
                width: double.infinity,
                height: 200,
                decoration: BoxDecoration(
                  color: AppColors.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Stack(
                  children: [
                    Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            "Let's Get Started!",
                            style: TextStyle(
                              fontSize: 28,
                              fontWeight: FontWeight.bold,
                              color: AppColors.primary,
                              fontFamily: 'Playfair Display',
                            ),
                          ),
                          const SizedBox(height: 10),
                          Text(
                            "All types of services for your pet in one place",
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              fontSize: 16,
                              color: AppColors.secondary,
                            ),
                          ),
                        ],
                      ),
                    ),
                    // Pet illustrations
                    Positioned(
                      top: 20,
                      left: 20,
                      child: _buildPetIcon(
                          '🐕', AppColors.primary.withOpacity(0.2)),
                    ),
                    Positioned(
                      top: 20,
                      right: 20,
                      child: _buildPetIcon(
                          '🐱', AppColors.secondary.withOpacity(0.2)),
                    ),
                    Positioned(
                      bottom: 20,
                      left: 40,
                      child: _buildPetIcon(
                          '🐰', AppColors.primary.withOpacity(0.15)),
                    ),
                    Positioned(
                      bottom: 20,
                      right: 40,
                      child: _buildPetIcon(
                          '🐦', AppColors.secondary.withOpacity(0.15)),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 30),

              // Create Profile Button
              Center(
                child: Container(
                  width: double.infinity,
                  height: 60,
                  decoration: BoxDecoration(
                    color: AppColors.secondary,
                    borderRadius: BorderRadius.circular(15),
                  ),
                  child: TextButton(
                    onPressed: () {
                      // TODO: Navigate to profile creation or show profile modal
                    },
                    child: Text(
                      'Create Profile',
                      style: TextStyle(
                        color: AppColors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ),

              const SizedBox(height: 30),

              // Search Services Nearby
              Text(
                'Search Services Nearby',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.w600,
                  color: AppColors.secondary,
                ),
              ),

              const SizedBox(height: 20),

              // Service Categories Grid
              Consumer<CategoryProvider>(
                builder: (context, categoryProvider, child) {
                  if (categoryProvider.isLoading) {
                    return const Center(
                      child: CircularProgressIndicator(),
                    );
                  }

                  if (categoryProvider.error != null) {
                    return Center(
                      child: Column(
                        children: [
                          Text(
                            'Error loading categories',
                            style: TextStyle(color: AppColors.secondary),
                          ),
                          const SizedBox(height: 10),
                          ElevatedButton(
                            onPressed: () {
                              categoryProvider.fetchCategories();
                            },
                            child: const Text('Retry'),
                          ),
                        ],
                      ),
                    );
                  }

                  final categories = categoryProvider.categories;

                  if (categories.isEmpty) {
                    return _buildDefaultCategories();
                  }

                  return GridView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    gridDelegate:
                        const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 2,
                      crossAxisSpacing: 15,
                      mainAxisSpacing: 15,
                      childAspectRatio: 1.2,
                    ),
                    itemCount: categories.length,
                    itemBuilder: (context, index) {
                      final category = categories[index];
                      return _buildCategoryCard(
                        category.name,
                        category.icon,
                        _parseColor(category.color),
                        () {
                          // TODO: Navigate to category services
                          print('Tapped on ${category.name}');
                        },
                      );
                    },
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPetIcon(String emoji, Color backgroundColor) {
    return Container(
      width: 50,
      height: 50,
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(25),
      ),
      child: Center(
        child: Text(
          emoji,
          style: const TextStyle(fontSize: 24),
        ),
      ),
    );
  }

  Widget _buildDefaultCategories() {
    final defaultCategories = [
      {'name': 'Sitting', 'icon': '🏠', 'color': const Color(0xFFe8f5e8)},
      {'name': 'Health', 'icon': '🏥', 'color': const Color(0xFFe8f0ff)},
      {'name': 'Boarding', 'icon': '🏨', 'color': const Color(0xFFffe8e8)},
      {'name': 'Training', 'icon': '🎓', 'color': const Color(0xFFfff8e8)},
      {'name': 'Grooming', 'icon': '✂️', 'color': const Color(0xFFf8e8ff)},
      {'name': 'Walking', 'icon': '🚶', 'color': const Color(0xFFe8fff8)},
    ];

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 15,
        mainAxisSpacing: 15,
        childAspectRatio: 1.2,
      ),
      itemCount: defaultCategories.length,
      itemBuilder: (context, index) {
        final category = defaultCategories[index];
        return _buildCategoryCard(
          category['name'] as String,
          category['icon'] as String,
          category['color'] as Color,
          () {
            print('Tapped on ${category['name']}');
          },
        );
      },
    );
  }

  Widget _buildCategoryCard(
      String title, String icon, Color backgroundColor, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(15),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.8),
                borderRadius: BorderRadius.circular(30),
              ),
              child: Center(
                child: Text(
                  icon,
                  style: const TextStyle(fontSize: 28),
                ),
              ),
            ),
            const SizedBox(height: 12),
            Text(
              title,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: AppColors.secondary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _parseColor(String colorString) {
    try {
      // Remove # if present
      String cleanColor = colorString.replaceAll('#', '');

      // Add alpha if not present
      if (cleanColor.length == 6) {
        cleanColor = 'FF$cleanColor';
      }

      return Color(int.parse(cleanColor, radix: 16));
    } catch (e) {
      // Return default color if parsing fails
      return const Color(0xFF007bff);
    }
  }
}
