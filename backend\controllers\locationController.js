const User = require('../models/User');

// Update business location
exports.updateBusinessLocation = async (req, res) => {
  try {
    const { latitude, longitude, address } = req.body;
    const userId = req.user.id;

    // Validate input
    if (!latitude || !longitude) {
      return res.status(400).json({ 
        message: 'Latitude and longitude are required' 
      });
    }

    // Validate coordinates
    if (latitude < -90 || latitude > 90 || longitude < -180 || longitude > 180) {
      return res.status(400).json({ 
        message: 'Invalid coordinates' 
      });
    }

    // Find user and check if they are a business
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    if (user.userType !== 'Business') {
      return res.status(403).json({ 
        message: 'Only business users can set location' 
      });
    }

    // Update location
    user.location = {
      type: 'Point',
      coordinates: [longitude, latitude], // GeoJSON format: [lng, lat]
      address: address || '',
      isLocationSet: true
    };

    await user.save();

    res.status(200).json({
      message: 'Location updated successfully',
      location: {
        latitude,
        longitude,
        address: address || '',
        isLocationSet: true
      }
    });

  } catch (error) {
    console.error('Update location error:', error);
    res.status(500).json({ 
      message: 'Error updating location', 
      error: error.message 
    });
  }
};

// Get business location
exports.getBusinessLocation = async (req, res) => {
  try {
    const userId = req.user.id;

    const user = await User.findById(userId).select('location userType');
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    if (user.userType !== 'Business') {
      return res.status(403).json({ 
        message: 'Only business users have location data' 
      });
    }

    const location = user.location || {};
    
    res.status(200).json({
      message: 'Location retrieved successfully',
      location: {
        latitude: location.coordinates ? location.coordinates[1] : null,
        longitude: location.coordinates ? location.coordinates[0] : null,
        address: location.address || '',
        isLocationSet: location.isLocationSet || false
      }
    });

  } catch (error) {
    console.error('Get location error:', error);
    res.status(500).json({ 
      message: 'Error retrieving location', 
      error: error.message 
    });
  }
};

// Get nearby businesses
exports.getNearbyBusinesses = async (req, res) => {
  try {
    const { latitude, longitude, radius = 10000 } = req.query; // radius in meters, default 10km

    // Validate input
    if (!latitude || !longitude) {
      return res.status(400).json({ 
        message: 'Latitude and longitude are required' 
      });
    }

    const lat = parseFloat(latitude);
    const lng = parseFloat(longitude);

    // Validate coordinates
    if (lat < -90 || lat > 90 || lng < -180 || lng > 180) {
      return res.status(400).json({ 
        message: 'Invalid coordinates' 
      });
    }

    // Find nearby businesses using geospatial query
    const businesses = await User.find({
      userType: 'Business',
      'location.isLocationSet': true,
      location: {
        $near: {
          $geometry: {
            type: 'Point',
            coordinates: [lng, lat]
          },
          $maxDistance: parseInt(radius)
        }
      }
    }).select('name email phoneNumber shopImage shopOpenTime shopCloseTime location');

    // Format response
    const formattedBusinesses = businesses.map(business => ({
      id: business._id,
      name: business.name,
      email: business.email,
      phoneNumber: business.phoneNumber,
      shopImage: business.shopImage,
      shopOpenTime: business.shopOpenTime,
      shopCloseTime: business.shopCloseTime,
      location: {
        latitude: business.location.coordinates[1],
        longitude: business.location.coordinates[0],
        address: business.location.address
      }
    }));

    res.status(200).json({
      message: 'Nearby businesses retrieved successfully',
      businesses: formattedBusinesses,
      count: formattedBusinesses.length
    });

  } catch (error) {
    console.error('Get nearby businesses error:', error);
    res.status(500).json({ 
      message: 'Error retrieving nearby businesses', 
      error: error.message 
    });
  }
};

// Get all businesses with locations (for map view)
exports.getAllBusinessLocations = async (req, res) => {
  try {
    const businesses = await User.find({
      userType: 'Business',
      'location.isLocationSet': true
    }).select('name email phoneNumber shopImage shopOpenTime shopCloseTime location');

    // Format response
    const formattedBusinesses = businesses.map(business => ({
      id: business._id,
      name: business.name,
      email: business.email,
      phoneNumber: business.phoneNumber,
      shopImage: business.shopImage,
      shopOpenTime: business.shopOpenTime,
      shopCloseTime: business.shopCloseTime,
      location: {
        latitude: business.location.coordinates[1],
        longitude: business.location.coordinates[0],
        address: business.location.address
      }
    }));

    res.status(200).json({
      message: 'Business locations retrieved successfully',
      businesses: formattedBusinesses,
      count: formattedBusinesses.length
    });

  } catch (error) {
    console.error('Get all business locations error:', error);
    res.status(500).json({ 
      message: 'Error retrieving business locations', 
      error: error.message 
    });
  }
};

// Delete business location
exports.deleteBusinessLocation = async (req, res) => {
  try {
    const userId = req.user.id;

    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    if (user.userType !== 'Business') {
      return res.status(403).json({ 
        message: 'Only business users can delete location' 
      });
    }

    // Reset location
    user.location = {
      type: 'Point',
      coordinates: [0, 0],
      address: '',
      isLocationSet: false
    };

    await user.save();

    res.status(200).json({
      message: 'Location deleted successfully'
    });

  } catch (error) {
    console.error('Delete location error:', error);
    res.status(500).json({ 
      message: 'Error deleting location', 
      error: error.message 
    });
  }
};
