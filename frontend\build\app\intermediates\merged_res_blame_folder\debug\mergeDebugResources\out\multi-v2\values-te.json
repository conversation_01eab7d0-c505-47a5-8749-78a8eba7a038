{"logs": [{"outputFile": "com.example.petcare.app-mergeDebugResources-26:/values-te/values-te.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0a800a360ad9d097b56329a7af7139d0\\transformed\\browser-1.8.0\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,166,274,385", "endColumns": "110,107,110,106", "endOffsets": "161,269,380,487"}, "to": {"startLines": "9,10,11,12", "startColumns": "4,4,4,4", "startOffsets": "805,916,1024,1135", "endColumns": "110,107,110,106", "endOffsets": "911,1019,1130,1237"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\895788cfce406ee7adbc1ad1d8f4cebb\\transformed\\core-1.13.1\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,265,367,468,574,681,805", "endColumns": "101,107,101,100,105,106,123,100", "endOffsets": "152,260,362,463,569,676,800,901"}, "to": {"startLines": "2,3,4,5,6,7,8,13", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,265,367,468,574,681,1242", "endColumns": "101,107,101,100,105,106,123,100", "endOffsets": "152,260,362,463,569,676,800,1338"}}]}]}