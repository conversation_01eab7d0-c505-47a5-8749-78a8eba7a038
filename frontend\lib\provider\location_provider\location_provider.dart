import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import '../../utlis/app_config/app_config.dart';

class LocationProvider with ChangeNotifier {
  bool _isLoading = false;
  String? _error;
  Position? _currentPosition;
  String? _currentAddress;
  List<BusinessLocation> _nearbyBusinesses = [];
  List<BusinessLocation> _allBusinesses = [];
  BusinessLocation? _businessLocation;

  // Getters
  bool get isLoading => _isLoading;
  String? get error => _error;
  Position? get currentPosition => _currentPosition;
  String? get currentAddress => _currentAddress;
  List<BusinessLocation> get nearbyBusinesses => _nearbyBusinesses;
  List<BusinessLocation> get allBusinesses => _allBusinesses;
  BusinessLocation? get businessLocation => _businessLocation;

  // Get current location
  Future<void> getCurrentLocation() async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Check if location services are enabled
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        throw Exception('Location services are disabled.');
      }

      // Check location permissions
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          throw Exception('Location permissions are denied');
        }
      }

      if (permission == LocationPermission.deniedForever) {
        throw Exception('Location permissions are permanently denied');
      }

      // Get current position
      _currentPosition = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );

      // Get address from coordinates
      await _getAddressFromCoordinates(
        _currentPosition!.latitude,
        _currentPosition!.longitude,
      );

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
    }
  }

  // Get address from coordinates
  Future<void> _getAddressFromCoordinates(double latitude, double longitude) async {
    try {
      List<Placemark> placemarks = await placemarkFromCoordinates(latitude, longitude);
      if (placemarks.isNotEmpty) {
        Placemark place = placemarks[0];
        _currentAddress = '${place.street}, ${place.locality}, ${place.administrativeArea}';
      }
    } catch (e) {
      _currentAddress = 'Unknown location';
    }
  }

  // Update business location (for business users)
  Future<void> updateBusinessLocation(double latitude, double longitude, String address, String token) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final response = await http.put(
        Uri.parse('${AppConfig.baseUrl}/location/business/update'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: json.encode({
          'latitude': latitude,
          'longitude': longitude,
          'address': address,
        }),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        _businessLocation = BusinessLocation.fromJson({
          'id': 'current_user',
          'name': 'My Business',
          'location': data['location'],
        });
        _isLoading = false;
        notifyListeners();
      } else {
        throw Exception('Failed to update location');
      }
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
    }
  }

  // Get business location (for business users)
  Future<void> getBusinessLocation(String token) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final response = await http.get(
        Uri.parse('${AppConfig.baseUrl}/location/business/get'),
        headers: {
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['location']['isLocationSet']) {
          _businessLocation = BusinessLocation.fromJson({
            'id': 'current_user',
            'name': 'My Business',
            'location': data['location'],
          });
        }
        _isLoading = false;
        notifyListeners();
      } else {
        throw Exception('Failed to get location');
      }
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
    }
  }

  // Get nearby businesses
  Future<void> getNearbyBusinesses(double latitude, double longitude, {double radius = 10000}) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final response = await http.get(
        Uri.parse('${AppConfig.baseUrl}/location/nearby?latitude=$latitude&longitude=$longitude&radius=$radius'),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        _nearbyBusinesses = (data['businesses'] as List)
            .map((business) => BusinessLocation.fromJson(business))
            .toList();
        _isLoading = false;
        notifyListeners();
      } else {
        throw Exception('Failed to get nearby businesses');
      }
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
    }
  }

  // Get all businesses with locations
  Future<void> getAllBusinessLocations() async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final response = await http.get(
        Uri.parse('${AppConfig.baseUrl}/location/all-businesses'),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        _allBusinesses = (data['businesses'] as List)
            .map((business) => BusinessLocation.fromJson(business))
            .toList();
        _isLoading = false;
        notifyListeners();
      } else {
        throw Exception('Failed to get business locations');
      }
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
    }
  }

  // Delete business location
  Future<void> deleteBusinessLocation(String token) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final response = await http.delete(
        Uri.parse('${AppConfig.baseUrl}/location/business/delete'),
        headers: {
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        _businessLocation = null;
        _isLoading = false;
        notifyListeners();
      } else {
        throw Exception('Failed to delete location');
      }
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
    }
  }

  // Clear error
  void clearError() {
    _error = null;
    notifyListeners();
  }
}

// Business Location Model
class BusinessLocation {
  final String id;
  final String name;
  final String? email;
  final String? phoneNumber;
  final String? shopImage;
  final String? shopOpenTime;
  final String? shopCloseTime;
  final double latitude;
  final double longitude;
  final String address;

  BusinessLocation({
    required this.id,
    required this.name,
    this.email,
    this.phoneNumber,
    this.shopImage,
    this.shopOpenTime,
    this.shopCloseTime,
    required this.latitude,
    required this.longitude,
    required this.address,
  });

  factory BusinessLocation.fromJson(Map<String, dynamic> json) {
    return BusinessLocation(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      email: json['email'],
      phoneNumber: json['phoneNumber'],
      shopImage: json['shopImage'],
      shopOpenTime: json['shopOpenTime'],
      shopCloseTime: json['shopCloseTime'],
      latitude: json['location']['latitude']?.toDouble() ?? 0.0,
      longitude: json['location']['longitude']?.toDouble() ?? 0.0,
      address: json['location']['address'] ?? '',
    );
  }
}
