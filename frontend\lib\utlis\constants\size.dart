import 'package:flutter_screenutil/flutter_screenutil.dart';

class AppSizes {
  // Padding and margin sizes
  static double xs = 4.0.w;
  static double sm = 8.0.w;
  static double md = 16.0.w;
  static double lg = 24.0.w;
  static double xl = 32.0.w;

  // Icon sizes
  static double iconXs = 12.0.r;
  static double iconSm = 16.0.r;
  static double iconMd = 24.0.r;
  static double iconLg = 32.0.r;

  // Font sizes
  static const double fontSizeXs = 12.0;
  static const double fontSizeSm = 14.0;
  static const double fontSizeMd = 16.0;
  static const double fontSizeLg = 18.0;
  static const double fontSizeXl = 24.0;
  static const double fontSizeXXl = 32.0;


  // Button sizes
  static double buttonHeight = 18.0.h;
  static double buttonRadius = 12.0.r;
  static double buttonWidth = 120.0.w;

  // AppBar height
  static double appBarHeight = 56.0.h;

  // Image sizes
  static double imageThumbSize = 80.0.w;

  // Default spacing between sections
  static double defaultSpace = 24.0.h;
  static double spaceBtwItems = 16.0.h;
  static double spaceBtwSections = 32.0.h;

  // Border radius
  static double borderRadiusSm = 4.0.r;
  static double borderRadiusMd = 10.0.r;
  static double borderRadiusLg = 12.0.r;

  // Product item dimensions
  static double productImageSize = 120.0.w;
  static double productImageRadius = 16.0.r;
  static double productItemHeight = 160.0.h;

  // Divider height
  static double dividerHeight = 1.0.h;

  // Input field
  static double inputFieldRadius = 12.0.r;
  static double spaceBtwInputFields = 16.0.h;

  // Card sizes
  static double cardRadiusLg = 16.0.r;
  static double cardRadiusMd = 12.0.r;
  static double cardRadiusSm = 10.0.r;
  static double cardRadiusXs = 6.0.r;
  static double cardElevation = 2.0.w;

  // Image carousel height
  static double imageCarouselHeight = 200.0.h;

  // Loading indicator size
  static double loadingIndicatorSize = 36.0.r;

  // Grid view spacing
  static double gridViewSpacing = 16.0.w;
}

