import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:provider/provider.dart';
import 'package:get/get.dart';
import '../../../common/widgets/appbar/appbar.dart';
import '../../../utlis/constants/colors.dart';
import '../../../utlis/constants/size.dart';
import '../../../provider/location_provider/location_provider.dart';

class BusinessesMapScreen extends StatefulWidget {
  const BusinessesMapScreen({super.key});

  @override
  State<BusinessesMapScreen> createState() => _BusinessesMapScreenState();
}

class _BusinessesMapScreenState extends State<BusinessesMapScreen> {
  GoogleMapController? _mapController;
  Set<Marker> _markers = {};
  LatLng _currentLocation = LatLng(37.7749, -122.4194); // Default to San Francisco

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeMap();
    });
  }

  void _initializeMap() async {
    final locationProvider = Provider.of<LocationProvider>(context, listen: false);
    
    // Get current location
    await locationProvider.getCurrentLocation();
    if (locationProvider.currentPosition != null) {
      setState(() {
        _currentLocation = LatLng(
          locationProvider.currentPosition!.latitude,
          locationProvider.currentPosition!.longitude,
        );
      });
    }

    // Get all business locations
    await locationProvider.getAllBusinessLocations();
    _updateMarkers();
  }

  void _updateMarkers() {
    final locationProvider = Provider.of<LocationProvider>(context, listen: false);
    Set<Marker> markers = {};

    // Add current location marker
    if (locationProvider.currentPosition != null) {
      markers.add(
        Marker(
          markerId: MarkerId('current_location'),
          position: LatLng(
            locationProvider.currentPosition!.latitude,
            locationProvider.currentPosition!.longitude,
          ),
          icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueBlue),
          infoWindow: InfoWindow(
            title: 'Your Location',
            snippet: locationProvider.currentAddress ?? 'Current location',
          ),
        ),
      );
    }

    // Add business markers
    for (var business in locationProvider.allBusinesses) {
      markers.add(
        Marker(
          markerId: MarkerId(business.id),
          position: LatLng(business.latitude, business.longitude),
          icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueRed),
          infoWindow: InfoWindow(
            title: business.name,
            snippet: business.address,
          ),
          onTap: () => _showBusinessDetails(business),
        ),
      );
    }

    setState(() {
      _markers = markers;
    });
  }

  void _showBusinessDetails(BusinessLocation business) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: EdgeInsets.all(AppSizes.md),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              business.name,
              style: Theme.of(context).textTheme.titleLarge!.copyWith(
                color: AppColors.primary,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: AppSizes.sm),
            
            if (business.address.isNotEmpty) ...[
              Row(
                children: [
                  Icon(Icons.location_on, color: AppColors.primary, size: AppSizes.iconSm),
                  SizedBox(width: AppSizes.xs),
                  Expanded(
                    child: Text(
                      business.address,
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ),
                ],
              ),
              SizedBox(height: AppSizes.sm),
            ],
            
            if (business.phoneNumber != null) ...[
              Row(
                children: [
                  Icon(Icons.phone, color: AppColors.primary, size: AppSizes.iconSm),
                  SizedBox(width: AppSizes.xs),
                  Text(
                    business.phoneNumber!,
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ],
              ),
              SizedBox(height: AppSizes.sm),
            ],
            
            if (business.shopOpenTime != null && business.shopCloseTime != null) ...[
              Row(
                children: [
                  Icon(Icons.access_time, color: AppColors.primary, size: AppSizes.iconSm),
                  SizedBox(width: AppSizes.xs),
                  Text(
                    '${business.shopOpenTime} - ${business.shopCloseTime}',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ],
              ),
              SizedBox(height: AppSizes.md),
            ],
            
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () {
                      // Navigate to business profile or services
                      Get.back();
                      // You can add navigation to business details here
                    },
                    icon: Icon(Icons.info, color: AppColors.white),
                    label: Text('View Details'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: AppColors.white,
                    ),
                  ),
                ),
                SizedBox(width: AppSizes.sm),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () {
                      // Open in Google Maps
                      Get.back();
                      // You can add Google Maps navigation here
                    },
                    icon: Icon(Icons.directions, color: AppColors.primary),
                    label: Text('Directions'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.white,
                      foregroundColor: AppColors.primary,
                      side: BorderSide(color: AppColors.primary),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _goToCurrentLocation() async {
    final locationProvider = Provider.of<LocationProvider>(context, listen: false);
    if (locationProvider.currentPosition != null && _mapController != null) {
      await _mapController!.animateCamera(
        CameraUpdate.newLatLng(
          LatLng(
            locationProvider.currentPosition!.latitude,
            locationProvider.currentPosition!.longitude,
          ),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(title: 'Nearby Businesses'),
      body: Consumer<LocationProvider>(
        builder: (context, locationProvider, child) {
          if (locationProvider.isLoading) {
            return Center(child: CircularProgressIndicator());
          }

          return Stack(
            children: [
              GoogleMap(
                onMapCreated: (GoogleMapController controller) {
                  _mapController = controller;
                },
                initialCameraPosition: CameraPosition(
                  target: _currentLocation,
                  zoom: 12.0,
                ),
                markers: _markers,
                myLocationEnabled: true,
                myLocationButtonEnabled: false,
              ),
              
              // Floating action button for current location
              Positioned(
                bottom: AppSizes.xl,
                right: AppSizes.md,
                child: FloatingActionButton(
                  onPressed: _goToCurrentLocation,
                  backgroundColor: AppColors.primary,
                  child: Icon(Icons.my_location, color: AppColors.white),
                ),
              ),
              
              // Business count info
              Positioned(
                top: AppSizes.md,
                left: AppSizes.md,
                right: AppSizes.md,
                child: Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: AppSizes.md,
                    vertical: AppSizes.sm,
                  ),
                  decoration: BoxDecoration(
                    color: AppColors.white,
                    borderRadius: BorderRadius.circular(AppSizes.borderRadiusMd),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 4,
                        offset: Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Text(
                    '${locationProvider.allBusinesses.length} businesses found',
                    style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                      color: AppColors.primary,
                      fontWeight: FontWeight.w500,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}
