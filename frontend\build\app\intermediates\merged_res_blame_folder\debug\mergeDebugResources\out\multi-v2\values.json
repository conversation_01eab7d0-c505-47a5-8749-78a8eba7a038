{"logs": [{"outputFile": "com.example.petcare.app-mergeDebugResources-26:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e0b5531fb6071dd0c0637bf9e3ff30b3\\transformed\\jetified-window-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,3,9,17,25,37,43,49,50,51,52,53,54,55,61,66,74,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,287,506,725,1039,1227,1414,1467,1527,1579,1624,1663,1723,1918,2076,2358,2972", "endLines": "2,8,16,24,36,42,48,49,50,51,52,53,54,60,65,73,88,104", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "109,282,501,720,1034,1222,1409,1462,1522,1574,1619,1658,1718,1913,2071,2353,2967,3621"}, "to": {"startLines": "2,3,9,17,26,38,44,50,51,52,53,54,124,186,192,315,323,338", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,287,506,785,1099,1287,1474,1527,1587,1639,1684,6127,9859,10054,14296,14578,15192", "endLines": "2,8,16,24,37,43,49,50,51,52,53,54,124,191,196,322,337,353", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "109,282,501,720,1094,1282,1469,1522,1582,1634,1679,1718,6182,10049,10207,14573,15187,15841"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\6b36f81d5694734e678bc98bc2922293\\transformed\\jetified-activity-1.10.1\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "128,148", "startColumns": "4,4", "startOffsets": "6314,7366", "endColumns": "41,59", "endOffsets": "6351,7421"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e5e10a490c81eb47dd4b0bd61ded4fb2\\transformed\\jetified-savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "149", "startColumns": "4", "startOffsets": "7426", "endColumns": "53", "endOffsets": "7475"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0b55a7e361e8e5445b27692ec9c2e406\\transformed\\jetified-core-viewtree-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "146", "startColumns": "4", "startOffsets": "7280", "endColumns": "42", "endOffsets": "7318"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\895788cfce406ee7adbc1ad1d8f4cebb\\transformed\\core-1.13.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "25,55,56,61,62,63,64,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,126,127,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,152,154,155,156,157,158,159,160,165,173,174,178,179,183,184,185,197,203,213,246,276,309", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "725,1723,1795,2132,2197,2263,2332,2538,2608,2676,2748,2818,2879,2953,3026,3087,3148,3210,3274,3336,3397,3465,3565,3625,3691,3764,3833,3890,3942,4004,4076,4152,4217,4276,4335,4395,4455,4515,4575,4635,4695,4755,4815,4875,4935,4994,5054,5114,5174,5234,5294,5354,5414,5474,5534,5594,5653,5713,5773,5832,5891,5950,6009,6068,6244,6279,6421,6476,6539,6594,6652,6710,6771,6834,6891,6942,6992,7053,7110,7176,7210,7245,7594,7747,7814,7886,7955,8024,8098,8170,8541,8962,9079,9280,9390,9591,9720,9792,10212,10415,10716,12447,13447,14129", "endLines": "25,55,56,61,62,63,64,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,126,127,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,152,154,155,156,157,158,159,160,165,173,177,178,182,183,184,185,202,212,245,266,308,314", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "780,1790,1878,2192,2258,2327,2390,2603,2671,2743,2813,2874,2948,3021,3082,3143,3205,3269,3331,3392,3460,3560,3620,3686,3759,3828,3885,3937,3999,4071,4147,4212,4271,4330,4390,4450,4510,4570,4630,4690,4750,4810,4870,4930,4989,5049,5109,5169,5229,5289,5349,5409,5469,5529,5589,5648,5708,5768,5827,5886,5945,6004,6063,6122,6274,6309,6471,6534,6589,6647,6705,6766,6829,6886,6937,6987,7048,7105,7171,7205,7240,7275,7659,7809,7881,7950,8019,8093,8165,8253,8607,9074,9275,9385,9586,9715,9787,9854,10410,10711,12442,13123,14124,14291"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1fda9668f2fd9b3cd682e1fa054a93a4\\transformed\\fragment-1.7.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "125,129,151,267,272", "startColumns": "4,4,4,4,4", "startOffsets": "6187,6356,7530,13128,13298", "endLines": "125,129,151,271,275", "endColumns": "56,64,63,24,24", "endOffsets": "6239,6416,7589,13293,13442"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\6bc594fc1c6cb8ef3ce5ef9c2c4d216b\\transformed\\lifecycle-viewmodel-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "150", "startColumns": "4", "startOffsets": "7480", "endColumns": "49", "endOffsets": "7525"}}, {"source": "D:\\flutter project\\petdash\\petdash\\frontend\\android\\app\\src\\main\\res\\values\\styles.xml", "from": {"startLines": "3,14", "startColumns": "4,4", "startOffsets": "176,832", "endLines": "7,16", "endColumns": "12,12", "endOffsets": "483,998"}, "to": {"startLines": "166,170", "startColumns": "4,4", "startOffsets": "8612,8793", "endLines": "169,172", "endColumns": "12,12", "endOffsets": "8788,8957"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\032b4ea919cde9099094bfb2c7bf01c1\\transformed\\jetified-startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "153", "startColumns": "4", "startOffsets": "7664", "endColumns": "82", "endOffsets": "7742"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0a800a360ad9d097b56329a7af7139d0\\transformed\\browser-1.8.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,113,179,242,304,375,447,515,582,661", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "108,174,237,299,370,442,510,577,656,725"}, "to": {"startLines": "57,58,59,60,65,66,161,162,163,164", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "1883,1941,2007,2070,2395,2466,8258,8326,8393,8472", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "1936,2002,2065,2127,2461,2533,8321,8388,8467,8536"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f8a2aedcd3468911daec058ed5ad1526\\transformed\\lifecycle-runtime-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "147", "startColumns": "4", "startOffsets": "7323", "endColumns": "42", "endOffsets": "7361"}}]}]}