import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../../../../common/widgets/appbar/appbar.dart';
import '../../../../utlis/constants/colors.dart';
import '../../../../utlis/constants/image_strings.dart';
import '../../../../utlis/constants/size.dart';
import 'ServiceDetails.dart';

class ServicesList extends StatefulWidget {
  const ServicesList({super.key});

  @override
  State<ServicesList> createState() => _ServicesListState();
}

class _ServicesListState extends State<ServicesList> {
  final List<Map<String, dynamic>> serviceProviders = [
    {
      'name': 'Plush Paws',
      'distance': '.03 miles away',
      'rating': 5.0,
      'image': AppImages.store1,
      'logo': AppImages.storeLogo1,
      'openTime': 'Open at 8 AM–10PM',
    },
    {
      'name': 'Mr.aladyn',
      'distance': '25 miles away',
      'rating': 4.9,
      'image': AppImages.store2,
      'logo': AppImages.storeLogo2,
      'openTime': 'Open at 9 AM–9PM',
    },
    {
      'name': 'Pet Patch USA',
      'distance': '1 mile away',
      'rating': 4.7,
      'image': AppImages.store1,
      'logo': AppImages.storeLogo1,
      'openTime': 'Open at 10 AM–8PM',
    },
    {
      'name': 'Petido',
      'distance': '1.5 miles away',
      'rating': 4.6,
      'image': AppImages.store2,
      'logo': AppImages.storeLogo2,
      'openTime': 'Open at 8 AM–10PM',
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      appBar: CustomAppBar(title: 'Grooming'),
      body: Column(
        children: [
          // Filter & Sort
          Container(
            color: AppColors.white,
            padding: EdgeInsets.symmetric(
              horizontal: AppSizes.md,
              vertical: AppSizes.sm,
            ),
            child: Row(
              children: [
                Expanded(
                  child: Row(
                    children: [
                      Icon(Icons.sort, size: AppSizes.iconMd, color: AppColors.textprimaryColor),
                      SizedBox(width: AppSizes.xs),
                      Text(
                        'Sort',
                        style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                          color: AppColors.secondary,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  width: 1.w,
                  height: 20.h,
                  color: Colors.grey[300],
                ),
                Expanded(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.filter_list, size: AppSizes.iconMd, color: AppColors.textprimaryColor),
                      SizedBox(width: AppSizes.xs),
                      Text(
                        'Filter',
                        style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                          color: AppColors.secondary,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // List
          Expanded(
            child: ListView.builder(
              padding: EdgeInsets.all(AppSizes.md),
              itemCount: serviceProviders.length,
              itemBuilder: (context, index) {
                final provider = serviceProviders[index];
                return GestureDetector(
                  onTap: () {
                    Get.to(() => ServiceDetails(providerName: provider['name']));
                  },
                  child: Container(
                    margin: EdgeInsets.only(bottom: AppSizes.md),
                    decoration: BoxDecoration(
                      color: AppColors.white,
                      borderRadius: BorderRadius.circular(AppSizes.cardRadiusMd),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.grey.withOpacity(0.1),
                          spreadRadius: 1,
                          blurRadius: 5,
                          offset: Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Image + Rating
                        Stack(
                          children: [
                            ClipRRect(
                              borderRadius: BorderRadius.vertical(
                                top: Radius.circular(AppSizes.cardRadiusMd),
                              ),
                              child: Image.asset(
                                provider['image'],
                                height: 150.h,
                                width: double.infinity,
                                fit: BoxFit.cover,
                              ),
                            ),
                            Positioned(
                              top: AppSizes.sm,
                              left: AppSizes.sm,
                              child: Container(
                                padding: EdgeInsets.symmetric(
                                  horizontal: AppSizes.sm,
                                  vertical: 4.h,
                                ),
                                decoration: BoxDecoration(
                                  color: Colors.amber,
                                  borderRadius: BorderRadius.circular(20.r),
                                ),
                                child: Row(
                                  children: [
                                    Icon(Icons.star,
                                        color: AppColors.white, size: AppSizes.iconSm),
                                    SizedBox(width: 4.w),
                                    Text(
                                      provider['rating'].toString(),
                                      style: Theme.of(context).textTheme.bodySmall!.copyWith(
                                        color: AppColors.white,
                                        fontSize: AppSizes.fontSizeSm.sp,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),

                        // Info
                        Padding(
                          padding: EdgeInsets.all(AppSizes.md),
                          child: Row(
                            children: [
                              // Logo
                              Container(
                                width: 40.w,
                                height: 40.h,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(AppSizes.cardRadiusSm),
                                  image: DecorationImage(
                                    image: AssetImage(provider['logo']),
                                    fit: BoxFit.cover,
                                  ),
                                ),
                              ),
                              SizedBox(width: AppSizes.sm),

                              // Name + Open Time
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      provider['name'],
                                      style: Theme.of(context).textTheme.titleSmall!.copyWith(
                                        fontWeight: FontWeight.w600,
                                        fontSize: AppSizes.fontSizeMd.sp,
                                        color: AppColors.secondary,
                                      ),
                                    ),
                                    SizedBox(height: 4.h),
                                    Text(
                                      provider['openTime'],
                                      style: Theme.of(context).textTheme.bodySmall!.copyWith(
                                        color: AppColors.textprimaryColor,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ],
                                ),
                              ),

                              // Distance
                              Text(
                                provider['distance'],
                                style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                                  color: AppColors.textprimaryColor,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
