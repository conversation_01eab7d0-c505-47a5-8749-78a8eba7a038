import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:provider/provider.dart';
import 'package:get/get.dart';
import '../../../common/widgets/appbar/appbar.dart';
import '../../../common/widgets/Button/primarybutton.dart';
import '../../../utlis/constants/colors.dart';
import '../../../utlis/constants/size.dart';
import '../../../provider/location_provider/location_provider.dart';
import '../../../provider/auth_provider/loginprovider.dart';

class BusinessLocationScreen extends StatefulWidget {
  const BusinessLocationScreen({super.key});

  @override
  State<BusinessLocationScreen> createState() => _BusinessLocationScreenState();
}

class _BusinessLocationScreenState extends State<BusinessLocationScreen> {
  GoogleMapController? _mapController;
  LatLng? _selectedLocation;
  String _selectedAddress = '';
  final TextEditingController _addressController = TextEditingController();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeLocation();
    });
  }

  void _initializeLocation() async {
    final locationProvider = Provider.of<LocationProvider>(context, listen: false);
    final loginProvider = Provider.of<LoginProvider>(context, listen: false);
    
    if (loginProvider.token != null) {
      await locationProvider.getBusinessLocation(loginProvider.token!);
      if (locationProvider.businessLocation != null) {
        setState(() {
          _selectedLocation = LatLng(
            locationProvider.businessLocation!.latitude,
            locationProvider.businessLocation!.longitude,
          );
          _selectedAddress = locationProvider.businessLocation!.address;
          _addressController.text = _selectedAddress;
        });
      }
    }
    
    // Get current location if no business location is set
    if (_selectedLocation == null) {
      await locationProvider.getCurrentLocation();
      if (locationProvider.currentPosition != null) {
        setState(() {
          _selectedLocation = LatLng(
            locationProvider.currentPosition!.latitude,
            locationProvider.currentPosition!.longitude,
          );
          _selectedAddress = locationProvider.currentAddress ?? '';
          _addressController.text = _selectedAddress;
        });
      }
    }
  }

  void _onMapTap(LatLng location) {
    setState(() {
      _selectedLocation = location;
    });
    _getAddressFromLatLng(location);
  }

  void _getAddressFromLatLng(LatLng location) async {
    // You can implement reverse geocoding here
    // For now, we'll use a simple format
    setState(() {
      _selectedAddress = 'Lat: ${location.latitude.toStringAsFixed(6)}, Lng: ${location.longitude.toStringAsFixed(6)}';
      _addressController.text = _selectedAddress;
    });
  }

  void _saveLocation() async {
    if (_selectedLocation == null) {
      Get.snackbar('Error', 'Please select a location on the map');
      return;
    }

    final locationProvider = Provider.of<LocationProvider>(context, listen: false);
    final loginProvider = Provider.of<LoginProvider>(context, listen: false);

    if (loginProvider.token != null) {
      await locationProvider.updateBusinessLocation(
        _selectedLocation!.latitude,
        _selectedLocation!.longitude,
        _addressController.text.trim(),
        loginProvider.token!,
      );

      if (locationProvider.error == null) {
        Get.snackbar('Success', 'Location updated successfully');
        Get.back();
      } else {
        Get.snackbar('Error', locationProvider.error!);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(title: 'Set Business Location'),
      body: Consumer<LocationProvider>(
        builder: (context, locationProvider, child) {
          if (locationProvider.isLoading) {
            return Center(child: CircularProgressIndicator());
          }

          return Column(
            children: [
              // Map
              Expanded(
                flex: 3,
                child: _selectedLocation != null
                    ? GoogleMap(
                        onMapCreated: (GoogleMapController controller) {
                          _mapController = controller;
                        },
                        initialCameraPosition: CameraPosition(
                          target: _selectedLocation!,
                          zoom: 15.0,
                        ),
                        onTap: _onMapTap,
                        markers: _selectedLocation != null
                            ? {
                                Marker(
                                  markerId: MarkerId('selected_location'),
                                  position: _selectedLocation!,
                                  infoWindow: InfoWindow(
                                    title: 'Business Location',
                                    snippet: _selectedAddress,
                                  ),
                                ),
                              }
                            : {},
                      )
                    : Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            CircularProgressIndicator(),
                            SizedBox(height: AppSizes.md),
                            Text('Loading map...'),
                          ],
                        ),
                      ),
              ),

              // Address input and controls
              Expanded(
                flex: 1,
                child: Padding(
                  padding: EdgeInsets.all(AppSizes.md),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Address',
                        style: Theme.of(context).textTheme.titleMedium!.copyWith(
                          color: AppColors.primary,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      SizedBox(height: AppSizes.sm),
                      
                      // Address input field
                      TextField(
                        controller: _addressController,
                        decoration: InputDecoration(
                          hintText: 'Enter business address',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(AppSizes.borderRadiusMd),
                          ),
                          contentPadding: EdgeInsets.symmetric(
                            horizontal: AppSizes.md,
                            vertical: AppSizes.sm,
                          ),
                        ),
                        maxLines: 2,
                        onChanged: (value) {
                          _selectedAddress = value;
                        },
                      ),
                      
                      SizedBox(height: AppSizes.md),
                      
                      // Instructions
                      Text(
                        'Tap on the map to select your business location',
                        style: Theme.of(context).textTheme.bodySmall!.copyWith(
                          color: AppColors.borderColor,
                        ),
                      ),
                      
                      Spacer(),
                      
                      // Save button
                      PrimaryButton(
                        title: 'Save Location',
                        onPressed: _saveLocation,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  @override
  void dispose() {
    _addressController.dispose();
    super.dispose();
  }
}
