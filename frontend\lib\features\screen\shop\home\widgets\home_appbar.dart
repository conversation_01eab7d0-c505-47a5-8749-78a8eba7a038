import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:get/get.dart';
import '../../../../../utlis/constants/colors.dart';
import '../../../../../utlis/constants/size.dart';
import '../../../../../provider/location_provider/location_provider.dart';
import '../../../../../provider/auth_provider/loginprovider.dart';
import '../../location/business_location_screen.dart';
import '../../location/businesses_map_screen.dart';

class HomeAppBar extends StatelessWidget {
  const HomeAppBar({super.key});

  void _onLocationTap(BuildContext context) {
    final loginProvider = Provider.of<LoginProvider>(context, listen: false);

    if (loginProvider.token != null) {
      // Check user type from token or user data
      // For now, we'll show a dialog to choose action
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: Text('Location Options'),
          content: Text('What would you like to do?'),
          actions: [
            TextButton(
              onPressed: () {
                Get.back();
                Get.to(() => BusinessesMapScreen());
              },
              child: Text('View Businesses'),
            ),
            TextButton(
              onPressed: () {
                Get.back();
                Get.to(() => BusinessLocationScreen());
              },
              child: Text('Set My Location'),
            ),
          ],
        ),
      );
    } else {
      // User not logged in, just show map
      Get.to(() => BusinessesMapScreen());
    }
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        top: AppSizes.xl,
        left: AppSizes.md,
        right: AppSizes.md,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          GestureDetector(
            onTap: () => _onLocationTap(context),
            child: Row(
              children: [
                Icon(Icons.location_pin, size: AppSizes.iconMd, color: Colors.black),
                SizedBox(width: AppSizes.sm),
                Consumer<LocationProvider>(
                  builder: (context, locationProvider, child) {
                    return Text(
                      locationProvider.currentAddress ?? "My Location",
                      style: TextStyle(color: AppColors.primary),
                    );
                  },
                ),
              ],
            ),
          ),
          Stack(
            children: [
              Icon(Icons.notifications_none, size: AppSizes.iconMd),
              Positioned(
                top: 0,
                right: 0,
                child: Container(
                  width: 8.w,
                  height: 8.w,
                  decoration: const BoxDecoration(
                    color: Colors.blue,
                    shape: BoxShape.circle,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
