import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import '../utlis/app_config/app_config.dart';

class UserProfile {
  final String id;
  final String name;
  final String email;
  final String phoneNumber;
  final String streetName;
  final String zipCode;
  final String city;
  final String state;
  final String? profileImage;
  final String userType;

  UserProfile({
    required this.id,
    required this.name,
    required this.email,
    required this.phoneNumber,
    required this.streetName,
    required this.zipCode,
    required this.city,
    required this.state,
    this.profileImage,
    required this.userType,
  });

  factory UserProfile.fromJson(Map<String, dynamic> json) {
    return UserProfile(
      id: json['_id'] ?? '',
      name: json['name'] ?? '',
      email: json['email'] ?? '',
      phoneNumber: json['phoneNumber'] ?? '',
      streetName: json['streetName'] ?? '',
      zipCode: json['zipCode'] ?? '',
      city: json['city'] ?? '',
      state: json['state'] ?? '',
      profileImage: json['profileImage'],
      userType: json['userType'] ?? 'Pet Owner',
    );
  }
}

class ProfileProvider with ChangeNotifier {
  UserProfile? _profile;
  bool _isLoading = false;
  String? _error;

  UserProfile? get profile => _profile;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Get user profile
  Future<void> getProfile(String token) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final url = Uri.parse('${AppConfig.baseUrl}/profile/get-profile');
      final response = await http.get(
        url,
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['profile'] != null) {
          _profile = UserProfile.fromJson(data['profile']);
        }
        _isLoading = false;
        notifyListeners();
      } else {
        _isLoading = false;
        _error = 'Failed to load profile';
        notifyListeners();
      }
    } catch (e) {
      _isLoading = false;
      _error = 'Error: ${e.toString()}';
      notifyListeners();
    }
  }

  // Create or update profile
  Future<String?> updateProfile({
    required String token,
    required String name,
    required String phoneNumber,
    required String streetName,
    required String zipCode,
    required String city,
    required String state,
    File? profileImage,
  }) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final url = Uri.parse('${AppConfig.baseUrl}/profile/create-update-profile');
      
      // Create multipart request
      var request = http.MultipartRequest('PUT', url);
      
      // Add authorization header
      request.headers['Authorization'] = 'Bearer $token';
      
      // Add text fields
      request.fields['name'] = name;
      request.fields['phoneNumber'] = phoneNumber;
      request.fields['streetName'] = streetName;
      request.fields['zipCode'] = zipCode;
      request.fields['city'] = city;
      request.fields['state'] = state;
      
      // Add profile image if provided
      if (profileImage != null) {
        request.files.add(
          await http.MultipartFile.fromPath(
            'profileImage',
            profileImage.path,
          ),
        );
      }
      
      // Send request
      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['user'] != null) {
          _profile = UserProfile.fromJson(data['user']);
        }
        _isLoading = false;
        notifyListeners();
        return null; // Success
      } else {
        final errorData = json.decode(response.body);
        _isLoading = false;
        _error = errorData['message'] ?? 'Failed to update profile';
        notifyListeners();
        return _error;
      }
    } catch (e) {
      _isLoading = false;
      _error = 'Error: ${e.toString()}';
      notifyListeners();
      return _error;
    }
  }
}
