{"info": {"_postman_id": "petdesh-backend-collection-002", "name": "Petdesh API Collection", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "description": "Postman collection for Petdesh backend API."}, "item": [{"name": "<PERSON><PERSON>", "item": [{"name": "Signup", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\",\n  \"userType\": \"Business\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/signup", "host": ["{{baseUrl}}"], "path": ["auth", "signup"]}}, "response": []}, {"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/login", "host": ["{{baseUrl}}"], "path": ["auth", "login"]}}, "response": []}, {"name": "Request Password Reset", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/request-password-reset", "host": ["{{baseUrl}}"], "path": ["auth", "request-password-reset"]}}, "response": []}, {"name": "Reset Password", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"token\": \"<reset-token>\",\n  \"newPassword\": \"newpassword123\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/reset-password", "host": ["{{baseUrl}}"], "path": ["auth", "reset-password"]}}, "response": []}]}, {"name": "Profile", "item": [{"name": "Get Profile", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/profile/get-profile", "host": ["{{baseUrl}}"], "path": ["profile", "get-profile"]}}, "response": []}, {"name": "Update Profile", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "formdata", "formdata": [{"key": "name", "value": "<PERSON>", "type": "text"}, {"key": "phoneNumber", "value": "1234567890", "type": "text"}, {"key": "streetName", "value": "Main St", "type": "text"}, {"key": "zipCode", "value": "12345", "type": "text"}, {"key": "city", "value": "Metropolis", "type": "text"}, {"key": "state", "value": "CA", "type": "text"}, {"key": "profileImage", "type": "file"}]}, "url": {"raw": "{{baseUrl}}/profile/create-update-profile", "host": ["{{baseUrl}}"], "path": ["profile", "create-update-profile"]}}, "response": []}]}, {"name": "Pet Profile", "item": [{"name": "Create Pet Profile", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "formdata", "formdata": [{"key": "name", "value": "Troy", "type": "text"}, {"key": "species", "value": "Dog", "type": "text"}, {"key": "typeOfPet", "value": "Toy Terrier", "type": "text"}, {"key": "breed", "value": "Toy terrier", "type": "text"}, {"key": "weight", "value": "47LBS", "type": "text"}, {"key": "gender", "value": "Male", "type": "text"}, {"key": "birthday", "value": "2016-02-02", "type": "text"}, {"key": "allergies", "value": "[\"<PERSON><PERSON>\",\"Soy\",\"Egg\"]", "type": "text"}, {"key": "currentMedications", "value": "", "type": "text"}, {"key": "lastVaccinatedDate", "value": "2021-10-02", "type": "text"}, {"key": "vaccinations", "value": "[{\"name\":\"Rabies\",\"date\":\"2021-10-02\"}]", "type": "text"}, {"key": "favorite<PERSON>oys", "value": "[\"Toy Rope\"]", "type": "text"}, {"key": "neutered", "value": "true", "type": "text"}, {"key": "vaccinated", "value": "true", "type": "text"}, {"key": "friendlyWithDogs", "value": "true", "type": "text"}, {"key": "friendlyWithCats", "value": "false", "type": "text"}, {"key": "friendlyWithKidsUnder10", "value": "true", "type": "text"}, {"key": "friendlyWithKidsOver10", "value": "true", "type": "text"}, {"key": "microchipped", "value": "true", "type": "text"}, {"key": "purebred", "value": "true", "type": "text"}, {"key": "pottyTrained", "value": "true", "type": "text"}, {"key": "preferredVeterinarian", "value": "", "type": "text"}, {"key": "preferredPharmacy", "value": "", "type": "text"}, {"key": "preferredGroomer", "value": "", "type": "text"}, {"key": "favoriteDogPark", "value": "", "type": "text"}, {"key": "profileImage", "type": "file"}]}, "url": {"raw": "{{baseUrl}}/pet/create", "host": ["{{baseUrl}}"], "path": ["pet", "create"]}}, "response": []}, {"name": "Update Pet Profile", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "formdata", "formdata": [{"key": "name", "value": "Troy", "type": "text"}, {"key": "weight", "value": "50LBS", "type": "text"}, {"key": "profileImage", "type": "file"}]}, "url": {"raw": "{{baseUrl}}/pet/update/:id", "host": ["{{baseUrl}}"], "path": ["pet", "update", ":id"]}}, "response": []}, {"name": "Get Pet Profile", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/pet/:id", "host": ["{{baseUrl}}"], "path": ["pet", ":id"]}}, "response": []}]}, {"name": "Service (Business)", "item": [{"name": "Create Service", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "formdata", "formdata": [{"key": "title", "value": "Bath & Full Haircut", "type": "text"}, {"key": "description", "value": "For dogs who need a bath & haircut.", "type": "text"}, {"key": "serviceIncluded", "value": "Bath, Haircut", "type": "text"}, {"key": "notes", "value": "Special care for sensitive skin.", "type": "text"}, {"key": "price", "value": "45", "type": "text"}, {"key": "category", "value": "<categoryId>", "type": "text"}, {"key": "images", "type": "file"}, {"key": "availableFor[cats][]", "value": "Small", "type": "text"}, {"key": "availableFor[dogs][]", "value": "Medium", "type": "text"}]}, "url": {"raw": "{{baseUrl}}/service/create", "host": ["{{baseUrl}}"], "path": ["service", "create"]}}, "response": []}, {"name": "Update Service", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "formdata", "formdata": [{"key": "title", "value": "Bath & Full Haircut", "type": "text"}, {"key": "price", "value": "55", "type": "text"}, {"key": "category", "value": "<categoryId>", "type": "text"}, {"key": "images", "type": "file"}]}, "url": {"raw": "{{baseUrl}}/service/update/:id", "host": ["{{baseUrl}}"], "path": ["service", "update", ":id"]}}, "response": []}, {"name": "Get Service", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/service/:id", "host": ["{{baseUrl}}"], "path": ["service", ":id"]}}, "response": []}, {"name": "Get All Business Services", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/service/business/all", "host": ["{{baseUrl}}"], "path": ["service", "business", "all"]}}, "response": []}, {"name": "Delete Service", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/service/delete/:id", "host": ["{{baseUrl}}"], "path": ["service", "delete", ":id"]}}, "response": []}]}, {"name": "Category", "item": [{"name": "Get All Public Categories", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/category/public", "host": ["{{baseUrl}}"], "path": ["category", "public"]}}, "response": []}, {"name": "Get Single Category", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/category/:id", "host": ["{{baseUrl}}"], "path": ["category", ":id"]}}, "response": []}, {"name": "Create Category (Admin)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Sitting\",\n  \"description\": \"Pet sitting services\",\n  \"icon\": \"sitting-icon.png\",\n  \"color\": \"#007bff\"\n}"}, "url": {"raw": "{{baseUrl}}/category/create", "host": ["{{baseUrl}}"], "path": ["category", "create"]}}, "response": []}, {"name": "Get All Categories (Admin)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/category/admin/all", "host": ["{{baseUrl}}"], "path": ["category", "admin", "all"]}}, "response": []}, {"name": "Update Category (Admin)", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Sitting\",\n  \"description\": \"Updated description\",\n  \"icon\": \"sitting-icon.png\",\n  \"color\": \"#007bff\"\n}"}, "url": {"raw": "{{baseUrl}}/category/update/:id", "host": ["{{baseUrl}}"], "path": ["category", "update", ":id"]}}, "response": []}, {"name": "Delete Category (Admin)", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/category/delete/:id", "host": ["{{baseUrl}}"], "path": ["category", "delete", ":id"]}}, "response": []}, {"name": "Seed Categories (Admin)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/category/seed", "host": ["{{baseUrl}}"], "path": ["category", "seed"]}}, "response": []}]}, {"name": "Business", "item": [{"name": "Get Businesses by Category", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/business/category/:categoryId?page=1&limit=10&city=&state=&zipCode=", "host": ["{{baseUrl}}"], "path": ["business", "category", ":categoryId"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "city", "value": ""}, {"key": "state", "value": ""}, {"key": "zipCode", "value": ""}]}}, "response": []}, {"name": "Get Business Profile", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/business/profile/:businessId", "host": ["{{baseUrl}}"], "path": ["business", "profile", ":businessId"]}}, "response": []}, {"name": "Search Businesses", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/business/search?query=&category=&city=&state=&zipCode=&page=1&limit=10", "host": ["{{baseUrl}}"], "path": ["business", "search"], "query": [{"key": "query", "value": ""}, {"key": "category", "value": ""}, {"key": "city", "value": ""}, {"key": "state", "value": ""}, {"key": "zipCode", "value": ""}, {"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}, "response": []}]}, {"name": "Product Management", "item": [{"name": "Create Product", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Royal Canin Medium Breed Adult Dry Dog Food\",\n  \"description\": \"Complete nutrition for medium breed adult dogs\",\n  \"price\": 59.74,\n  \"manufacturer\": \"Royal Canin\",\n  \"shippingCost\": 5.99,\n  \"monthlyDeliveryPrice\": 55.00,\n  \"brand\": \"Royal Canin\",\n  \"itemWeight\": \"30 Pounds\",\n  \"itemForm\": \"Dry\",\n  \"ageRange\": \"Adult\",\n  \"breedRecommendation\": \"Medium Breeds\",\n  \"dietType\": \"Complete\",\n  \"images\": [\"https://example.com/image1.jpg\", \"https://example.com/image2.jpg\"],\n  \"stock\": 100,\n  \"subscriptionAvailable\": true,\n  \"category\": \"Dog Food\"\n}"}, "url": {"raw": "{{baseUrl}}/product", "host": ["{{baseUrl}}"], "path": ["product"]}}, "response": []}, {"name": "Get All Products", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/product", "host": ["{{baseUrl}}"], "path": ["product"]}}, "response": []}, {"name": "Search Products", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/product/search?q=royal&category=Dog Food&minPrice=50&maxPrice=100", "host": ["{{baseUrl}}"], "path": ["product", "search"], "query": [{"key": "q", "value": "royal"}, {"key": "category", "value": "Dog Food"}, {"key": "minPrice", "value": "50"}, {"key": "maxPrice", "value": "100"}]}}, "response": []}, {"name": "Get Products by Category", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/product/category/Dog Food", "host": ["{{baseUrl}}"], "path": ["product", "category", "Dog Food"]}}, "response": []}, {"name": "Get Product by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/product/{{productId}}", "host": ["{{baseUrl}}"], "path": ["product", "{{productId}}"]}}, "response": []}, {"name": "Update Product", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Product Name\",\n  \"price\": 65.99,\n  \"stock\": 150\n}"}, "url": {"raw": "{{baseUrl}}/product/{{productId}}", "host": ["{{baseUrl}}"], "path": ["product", "{{productId}}"]}}, "response": []}, {"name": "Delete Product", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/product/{{productId}}", "host": ["{{baseUrl}}"], "path": ["product", "{{productId}}"]}}, "response": []}, {"name": "Get Business Products", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/product/business/list", "host": ["{{baseUrl}}"], "path": ["product", "business", "list"]}}, "response": []}]}, {"name": "Cart Management", "item": [{"name": "Add to Cart", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{\n  \"productId\": \"{{productId}}\",\n  \"quantity\": 2,\n  \"subscription\": false\n}"}, "url": {"raw": "{{baseUrl}}/order/cart", "host": ["{{baseUrl}}"], "path": ["order", "cart"]}}, "response": []}, {"name": "Get Cart", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/order/cart", "host": ["{{baseUrl}}"], "path": ["order", "cart"]}}, "response": []}, {"name": "Update Cart Item", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{\n  \"productId\": \"{{productId}}\",\n  \"quantity\": 3\n}"}, "url": {"raw": "{{baseUrl}}/order/cart", "host": ["{{baseUrl}}"], "path": ["order", "cart"]}}, "response": []}, {"name": "Remove from Cart", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/order/cart/{{productId}}", "host": ["{{baseUrl}}"], "path": ["order", "cart", "{{productId}}"]}}, "response": []}, {"name": "Apply Promo Code", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{\n  \"promoCode\": \"SAVE10\"\n}"}, "url": {"raw": "{{baseUrl}}/order/cart/promo", "host": ["{{baseUrl}}"], "path": ["order", "cart", "promo"]}}, "response": []}]}, {"name": "Order Management", "item": [{"name": "Checkout", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{\n  \"shippingAddress\": {\n    \"street\": \"123 Main St\",\n    \"city\": \"New York\",\n    \"state\": \"NY\",\n    \"zipCode\": \"10001\",\n    \"country\": \"USA\"\n  },\n  \"paymentMethod\": \"Credit Card\"\n}"}, "url": {"raw": "{{baseUrl}}/order/orders", "host": ["{{baseUrl}}"], "path": ["order", "orders"]}}, "response": []}, {"name": "Get Orders", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/order/orders", "host": ["{{baseUrl}}"], "path": ["order", "orders"]}}, "response": []}, {"name": "Get Order Details", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/order/orders/{{orderNumber}}", "host": ["{{baseUrl}}"], "path": ["order", "orders", "{{orderNumber}}"]}}, "response": []}]}, {"name": "Subscription Management", "item": [{"name": "Create Subscription", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{\n  \"productId\": \"{{productId}}\",\n  \"deliveryFrequency\": \"monthly\"\n}"}, "url": {"raw": "{{baseUrl}}/subscription", "host": ["{{baseUrl}}"], "path": ["subscription"]}}, "response": []}, {"name": "Get User Subscriptions", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/subscription", "host": ["{{baseUrl}}"], "path": ["subscription"]}}, "response": []}, {"name": "Update Subscription", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{\n  \"deliveryFrequency\": \"weekly\",\n  \"quantity\": 2\n}"}, "url": {"raw": "{{baseUrl}}/subscription/{{subscriptionId}}", "host": ["{{baseUrl}}"], "path": ["subscription", "{{subscriptionId}}"]}}, "response": []}, {"name": "Cancel Subscription", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/subscription/{{subscriptionId}}", "host": ["{{baseUrl}}"], "path": ["subscription", "{{subscriptionId}}"]}}, "response": []}]}], "variable": [{"key": "baseUrl", "value": "http://localhost:5000/api"}, {"key": "token", "value": ""}, {"key": "productId", "value": ""}, {"key": "orderNumber", "value": ""}, {"key": "subscriptionId", "value": ""}]}