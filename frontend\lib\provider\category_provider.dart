import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import '../utlis/app_config/app_config.dart';

class Category {
  final String id;
  final String name;
  final String description;
  final String icon;
  final String color;
  final int order;
  final bool isActive;

  Category({
    required this.id,
    required this.name,
    required this.description,
    required this.icon,
    required this.color,
    required this.order,
    required this.isActive,
  });

  factory Category.fromJson(Map<String, dynamic> json) {
    return Category(
      id: json['_id'] ?? '',
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      icon: json['icon'] ?? '',
      color: json['color'] ?? '#007bff',
      order: json['order'] ?? 0,
      isActive: json['isActive'] ?? true,
    );
  }
}

class CategoryProvider with ChangeNotifier {
  List<Category> _categories = [];
  bool _isLoading = false;
  String? _error;

  List<Category> get categories => _categories;
  bool get isLoading => _isLoading;
  String? get error => _error;

  Future<void> fetchCategories() async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final url = Uri.parse('${AppConfig.baseUrl}/category/public');
      final response = await http.get(url);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        
        if (data['categories'] != null) {
          final List<dynamic> categoriesJson = data['categories'];
          _categories = categoriesJson
              .map((categoryJson) => Category.fromJson(categoryJson))
              .toList();
          
          // Sort categories by order
          _categories.sort((a, b) => a.order.compareTo(b.order));
        } else {
          _categories = [];
        }
        
        _isLoading = false;
        notifyListeners();
      } else {
        _isLoading = false;
        _error = 'Failed to load categories';
        notifyListeners();
      }
    } catch (e) {
      _isLoading = false;
      _error = 'Error: ${e.toString()}';
      notifyListeners();
    }
  }

  // Helper method to get category by ID
  Category? getCategoryById(String id) {
    try {
      return _categories.firstWhere((category) => category.id == id);
    } catch (e) {
      return null;
    }
  }
}
