1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.petcare"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->D:\flutter project\petdash\petdash\frontend\android\app\src\main\AndroidManifest.xml:2:5-66
15-->D:\flutter project\petdash\petdash\frontend\android\app\src\main\AndroidManifest.xml:2:22-64
16    <!--
17     Required to query activities that can process text, see:
18         https://developer.android.com/training/package-visibility and
19         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
20
21         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
22    -->
23    <queries>
23-->D:\flutter project\petdash\petdash\frontend\android\app\src\main\AndroidManifest.xml:41:5-46:15
24        <intent>
24-->D:\flutter project\petdash\petdash\frontend\android\app\src\main\AndroidManifest.xml:42:9-45:18
25            <action android:name="android.intent.action.PROCESS_TEXT" />
25-->D:\flutter project\petdash\petdash\frontend\android\app\src\main\AndroidManifest.xml:43:13-72
25-->D:\flutter project\petdash\petdash\frontend\android\app\src\main\AndroidManifest.xml:43:21-70
26
27            <data android:mimeType="text/plain" />
27-->D:\flutter project\petdash\petdash\frontend\android\app\src\main\AndroidManifest.xml:44:13-50
27-->D:\flutter project\petdash\petdash\frontend\android\app\src\main\AndroidManifest.xml:44:19-48
28        </intent>
29    </queries>
30
31    <permission
31-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\895788cfce406ee7adbc1ad1d8f4cebb\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
32        android:name="com.example.petcare.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
32-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\895788cfce406ee7adbc1ad1d8f4cebb\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
33        android:protectionLevel="signature" />
33-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\895788cfce406ee7adbc1ad1d8f4cebb\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
34
35    <uses-permission android:name="com.example.petcare.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
35-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\895788cfce406ee7adbc1ad1d8f4cebb\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
35-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\895788cfce406ee7adbc1ad1d8f4cebb\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
36
37    <application
38        android:name="android.app.Application"
39        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
39-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\895788cfce406ee7adbc1ad1d8f4cebb\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
40        android:debuggable="true"
41        android:enableOnBackInvokedCallback="true"
42        android:extractNativeLibs="true"
43        android:icon="@mipmap/ic_launcher"
44        android:label="petcare" >
45        <activity
46            android:name="com.example.petcare.MainActivity"
47            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
48            android:exported="true"
49            android:hardwareAccelerated="true"
50            android:launchMode="singleTop"
51            android:taskAffinity=""
52            android:theme="@style/LaunchTheme"
53            android:windowSoftInputMode="adjustResize" >
54
55            <!--
56                 Specifies an Android theme to apply to this Activity as soon as
57                 the Android process has started. This theme is visible to the user
58                 while the Flutter UI initializes. After that, this theme continues
59                 to determine the Window background behind the Flutter UI.
60            -->
61            <meta-data
62                android:name="io.flutter.embedding.android.NormalTheme"
63                android:resource="@style/NormalTheme" />
64
65            <intent-filter>
66                <action android:name="android.intent.action.MAIN" />
67
68                <category android:name="android.intent.category.LAUNCHER" />
69            </intent-filter>
70        </activity>
71        <!--
72             Don't delete the meta-data below.
73             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
74        -->
75        <meta-data
76            android:name="flutterEmbedding"
77            android:value="2" />
78
79        <provider
79-->[:image_picker_android] D:\flutter project\petdash\petdash\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-17:20
80            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
80-->[:image_picker_android] D:\flutter project\petdash\petdash\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-82
81            android:authorities="com.example.petcare.flutter.image_provider"
81-->[:image_picker_android] D:\flutter project\petdash\petdash\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-74
82            android:exported="false"
82-->[:image_picker_android] D:\flutter project\petdash\petdash\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
83            android:grantUriPermissions="true" >
83-->[:image_picker_android] D:\flutter project\petdash\petdash\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-47
84            <meta-data
84-->[:image_picker_android] D:\flutter project\petdash\petdash\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:75
85                android:name="android.support.FILE_PROVIDER_PATHS"
85-->[:image_picker_android] D:\flutter project\petdash\petdash\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-67
86                android:resource="@xml/flutter_image_picker_file_paths" />
86-->[:image_picker_android] D:\flutter project\petdash\petdash\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:17-72
87        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
88        <service
88-->[:image_picker_android] D:\flutter project\petdash\petdash\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:19
89            android:name="com.google.android.gms.metadata.ModuleDependencies"
89-->[:image_picker_android] D:\flutter project\petdash\petdash\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-78
90            android:enabled="false"
90-->[:image_picker_android] D:\flutter project\petdash\petdash\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-36
91            android:exported="false" >
91-->[:image_picker_android] D:\flutter project\petdash\petdash\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
92            <intent-filter>
92-->[:image_picker_android] D:\flutter project\petdash\petdash\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-26:29
93                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
93-->[:image_picker_android] D:\flutter project\petdash\petdash\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-94
93-->[:image_picker_android] D:\flutter project\petdash\petdash\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-91
94            </intent-filter>
95
96            <meta-data
96-->[:image_picker_android] D:\flutter project\petdash\petdash\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-30:36
97                android:name="photopicker_activity:0:required"
97-->[:image_picker_android] D:\flutter project\petdash\petdash\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-63
98                android:value="" />
98-->[:image_picker_android] D:\flutter project\petdash\petdash\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-33
99        </service>
100
101        <activity
101-->[:url_launcher_android] D:\flutter project\petdash\petdash\frontend\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-11:74
102            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
102-->[:url_launcher_android] D:\flutter project\petdash\petdash\frontend\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-74
103            android:exported="false"
103-->[:url_launcher_android] D:\flutter project\petdash\petdash\frontend\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-37
104            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
104-->[:url_launcher_android] D:\flutter project\petdash\petdash\frontend\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-71
105
106        <uses-library
106-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e0b5531fb6071dd0c0637bf9e3ff30b3\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
107            android:name="androidx.window.extensions"
107-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e0b5531fb6071dd0c0637bf9e3ff30b3\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
108            android:required="false" />
108-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e0b5531fb6071dd0c0637bf9e3ff30b3\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
109        <uses-library
109-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e0b5531fb6071dd0c0637bf9e3ff30b3\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
110            android:name="androidx.window.sidecar"
110-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e0b5531fb6071dd0c0637bf9e3ff30b3\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
111            android:required="false" />
111-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e0b5531fb6071dd0c0637bf9e3ff30b3\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
112
113        <provider
113-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dd5fd3a0ceb20921a62658cb946f6c44\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
114            android:name="androidx.startup.InitializationProvider"
114-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dd5fd3a0ceb20921a62658cb946f6c44\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
115            android:authorities="com.example.petcare.androidx-startup"
115-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dd5fd3a0ceb20921a62658cb946f6c44\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
116            android:exported="false" >
116-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dd5fd3a0ceb20921a62658cb946f6c44\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
117            <meta-data
117-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dd5fd3a0ceb20921a62658cb946f6c44\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
118                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
118-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dd5fd3a0ceb20921a62658cb946f6c44\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
119                android:value="androidx.startup" />
119-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dd5fd3a0ceb20921a62658cb946f6c44\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
120            <meta-data
120-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ff19eef5016a9a5eff52d2000cbb8b10\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
121                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
121-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ff19eef5016a9a5eff52d2000cbb8b10\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
122                android:value="androidx.startup" />
122-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ff19eef5016a9a5eff52d2000cbb8b10\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
123        </provider>
124
125        <receiver
125-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ff19eef5016a9a5eff52d2000cbb8b10\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
126            android:name="androidx.profileinstaller.ProfileInstallReceiver"
126-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ff19eef5016a9a5eff52d2000cbb8b10\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
127            android:directBootAware="false"
127-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ff19eef5016a9a5eff52d2000cbb8b10\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
128            android:enabled="true"
128-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ff19eef5016a9a5eff52d2000cbb8b10\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
129            android:exported="true"
129-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ff19eef5016a9a5eff52d2000cbb8b10\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
130            android:permission="android.permission.DUMP" >
130-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ff19eef5016a9a5eff52d2000cbb8b10\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
131            <intent-filter>
131-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ff19eef5016a9a5eff52d2000cbb8b10\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
132                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
132-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ff19eef5016a9a5eff52d2000cbb8b10\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
132-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ff19eef5016a9a5eff52d2000cbb8b10\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
133            </intent-filter>
134            <intent-filter>
134-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ff19eef5016a9a5eff52d2000cbb8b10\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
135                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
135-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ff19eef5016a9a5eff52d2000cbb8b10\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
135-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ff19eef5016a9a5eff52d2000cbb8b10\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
136            </intent-filter>
137            <intent-filter>
137-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ff19eef5016a9a5eff52d2000cbb8b10\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
138                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
138-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ff19eef5016a9a5eff52d2000cbb8b10\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
138-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ff19eef5016a9a5eff52d2000cbb8b10\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
139            </intent-filter>
140            <intent-filter>
140-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ff19eef5016a9a5eff52d2000cbb8b10\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
141                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
141-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ff19eef5016a9a5eff52d2000cbb8b10\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
141-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ff19eef5016a9a5eff52d2000cbb8b10\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
142            </intent-filter>
143        </receiver>
144    </application>
145
146</manifest>
